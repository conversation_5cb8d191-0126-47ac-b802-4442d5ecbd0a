using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class CommanditaireRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }
            HashSet<Commanditaire> commanditaires = new HashSet<Commanditaire>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Commanditaire", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Commanditaire commanditaire = new Commanditaire()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Externe = Convert.ToBoolean(reader["externe"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Personnes = GetPersonnesForCommanditaire(Convert.ToInt64(reader["oid"]))
                            };

                            commanditaires.Add(commanditaire);
                        }
                    }
                }

                return commanditaires.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Commanditaire WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Commanditaire commanditaire = new Commanditaire()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Externe = Convert.ToBoolean(reader["externe"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Personnes = GetPersonnesForCommanditaire(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)commanditaire;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var commanditaire = entity as Commanditaire;
            if (commanditaire == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO Commanditaire (oid, __version, externe, nom, email, telephone) VALUES (@oid, @__version, @externe, @nom, @email, @telephone)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", commanditaire.Oid);
                    command.Parameters.AddWithValue("@__version", commanditaire.__version);
                    command.Parameters.AddWithValue("@externe", commanditaire.Externe);
                    command.Parameters.AddWithValue("@nom", commanditaire.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", commanditaire.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", commanditaire.Telephone ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var commanditaire = entity as Commanditaire;
            if (commanditaire == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE Commanditaire SET __version=@__version, externe=@externe, nom=@nom, email=@email, telephone=@telephone WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", commanditaire.Oid);
                    command.Parameters.AddWithValue("@__version", commanditaire.__version);
                    command.Parameters.AddWithValue("@externe", commanditaire.Externe);
                    command.Parameters.AddWithValue("@nom", commanditaire.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", commanditaire.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", commanditaire.Telephone ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }
            var commanditaire = entity as Commanditaire;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Commanditaire WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", commanditaire.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Commanditaire))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Commanditaire).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Commanditaire WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private HashSet<Personne> GetPersonnesForCommanditaire(long oidCommanditaire)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Personne WHERE oidCommanditaire = @oidCommanditaire", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oidCommanditaire", oidCommanditaire);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<Personne> personnes = new HashSet<Personne>();
                        while (reader.Read())
                        {
                            Personne personne = new Personne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Externe = Convert.ToBoolean(reader["externe"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Prenom = reader["prenom"].ToString() ?? string.Empty,
                                Fonction = reader["fonction"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Mobile = reader["mobile"].ToString() ?? string.Empty
                            };
                            personnes.Add(personne);
                        }
                        return personnes;
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
        }
    }
}
