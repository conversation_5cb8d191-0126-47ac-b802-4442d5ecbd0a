using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class MissionRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            HashSet<Mission> missions = new HashSet<Mission>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Mission", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Mission mission = new Mission()
                            {
                                TypeMission = GetTypeMissionForMission(Convert.ToInt64(reader["oidTypeMission"])),
                                ParentMission = GetParentMissionForMission(Convert.ToInt64(reader["oidParentMission"])),
                                SubMissions = GetSubMissionsForMission(Convert.ToInt64(reader["oid"]))
                            };

                            missions.Add(mission);
                        }
                    }
                }

                return missions.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Mission mission = new Mission()
                            {
                                TypeMission = GetTypeMissionForMission(Convert.ToInt64(reader["oidTypeMission"])),
                                ParentMission = GetParentMissionForMission(Convert.ToInt64(reader["oidParentMission"])),
                                SubMissions = GetSubMissionsForMission(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)mission;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private TypeMission GetTypeMissionForMission(long oidTypeMission)
        {
            if (oidTypeMission <= 0) return null;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM TypeMission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidTypeMission);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new TypeMission()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = new HashSet<DomaineMetier>() // Could be populated separately if needed
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return null;
        }

        private Mission GetParentMissionForMission(long oidParentMission)
        {
            if (oidParentMission <= 0) return null;

            bool shouldCloseConnection = false;
            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidParentMission);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Mission()
                            {
                                TypeMission = null, // Avoid deep recursion - load separately if needed
                                ParentMission = null, // Avoid deep recursion
                                SubMissions = new HashSet<Mission>() // Empty to avoid deep recursion
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return null;
        }

        private HashSet<Mission> GetSubMissionsForMission(long oidMission)
        {
            if (oidMission <= 0) return new HashSet<Mission>();

            HashSet<Mission> subMissions = new HashSet<Mission>();
            bool shouldCloseConnection = false;

            try
            {
                if (this._db.MySQLConn.State != System.Data.ConnectionState.Open)
                {
                    this._db.MySQLConn.Open();
                    shouldCloseConnection = true;
                }

                using (var command = new MySqlCommand("SELECT * FROM Mission WHERE oidParentMission = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidMission);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Mission subMission = new Mission()
                            {
                                TypeMission = null, // Avoid deep recursion - load separately if needed
                                ParentMission = null, // Avoid deep recursion
                                SubMissions = new HashSet<Mission>() // Empty to avoid deep recursion
                            };
                            subMissions.Add(subMission);
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }

            return subMissions;
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var mission = entity as Mission;
            if (mission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                // this._db.MySQLConn.Open();
                // string sql = @"INSERT INTO Mission (oidTypeMission, oidParentMission) VALUES (@oidTypeMission, @oidParentMission)";
                // using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                // {
                //     command.Parameters.AddWithValue("@oidTypeMission", mission.typeMission?.oid ?? (object)DBNull.Value);
                //     command.Parameters.AddWithValue("@oidParentMission", mission.parentMission.oid ?? (object)DBNull.Value);
                //     int result = command.ExecuteNonQuery();
                //     return result > 0;
                // }
                return false;
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var mission = entity as Mission;
            if (mission == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            // Note: This implementation assumes Mission has an oid field for identification
            // You may need to modify this based on your actual Mission model
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE Mission SET oidTypeMission=@oidTypeMission, oidParentMission=@oidParentMission WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    // command.Parameters.AddWithValue("@oid", mission.oid); // You'll need to add oid to Mission model
                    command.Parameters.AddWithValue("@oidTypeMission", mission.TypeMission?.Oid ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidParentMission", mission.ParentMission?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            var mission = entity as Mission;
            try
            {
                this._db.MySQLConn.Open();
                // using (var command = new MySqlCommand("DELETE FROM Mission WHERE oid = @id", this._db.MySQLConn))
                // {
                //     command.Parameters.AddWithValue("@id", mission.oid); // You'll need to add oid to Mission model
                //     int result = command.ExecuteNonQuery();
                //     return result > 0;
                // }
                throw new NotImplementedException("Delete requires Mission to have an oid property for identification");
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Mission))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Mission).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Mission WHERE oid = @id", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }
    }
}