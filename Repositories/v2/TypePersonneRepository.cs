using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypePersonneRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }
            HashSet<TypePersonne> typePersonnes = new HashSet<TypePersonne>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM TypePersonne", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TypePersonne typePersonne = new TypePersonne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                Interne = Convert.ToBoolean(reader["interne"]),
                                DomainesMetier = GetDomaineMetierForTypePersonne(Convert.ToInt64(reader["oid"]))
                            };

                            typePersonnes.Add(typePersonne);
                        }
                    }
                }

                return typePersonnes.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM TypePersonne WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            TypePersonne typePersonne = new TypePersonne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                Interne = Convert.ToBoolean(reader["interne"]),
                                DomainesMetier = GetDomaineMetierForTypePersonne(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)typePersonne;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var typePersonne = entity as TypePersonne;
            if (typePersonne == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO TypePersonne (oid, __version, code, libelle, description, obsolete, interne) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete, @interne)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", typePersonne.Oid);
                    command.Parameters.AddWithValue("@__version", typePersonne.__version);
                    command.Parameters.AddWithValue("@code", typePersonne.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typePersonne.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typePersonne.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typePersonne.Obsolete);
                    command.Parameters.AddWithValue("@interne", typePersonne.Interne);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var typePersonne = entity as TypePersonne;
            if (typePersonne == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE TypePersonne SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete, interne=@interne WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", typePersonne.Oid);
                    command.Parameters.AddWithValue("@__version", typePersonne.__version);
                    command.Parameters.AddWithValue("@code", typePersonne.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typePersonne.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typePersonne.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typePersonne.Obsolete);
                    command.Parameters.AddWithValue("@interne", typePersonne.Interne);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }
            var typePersonne = entity as TypePersonne;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM TypePersonne WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", typePersonne.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(TypePersonne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypePersonne).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM TypePersonne WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomaineMetierForTypePersonne(long oidTypePersonne)
        {
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();

                // Assuming there's a junction table for the many-to-many relationship
                using (var command = new MySqlCommand(@"
                    SELECT dm.* FROM DomaineMetier dm 
                    INNER JOIN TypePersonne_DomaineMetier tpdm ON dm.oid = tpdm.oidDomaineMetier 
                    WHERE tpdm.oidTypePersonne = @oidTypePersonne", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oidTypePersonne", oidTypePersonne);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
            
            return domainesMetier;
        }
    }
}
