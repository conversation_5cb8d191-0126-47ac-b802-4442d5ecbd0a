using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class JournalRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }
            HashSet<Journal> journals = new HashSet<Journal>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Journal", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Journal journal = new Journal()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Prive = Convert.ToBoolean(reader["prive"]),
                                Interne = Convert.ToBoolean(reader["interne"]),
                                Issue = GetIssueForJournal(Convert.ToInt64(reader["oidIssue"]))
                            };

                            journals.Add(journal);
                        }
                    }
                }

                return journals.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Journal WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Journal journal = new Journal()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Prive = Convert.ToBoolean(reader["prive"]),
                                Interne = Convert.ToBoolean(reader["interne"]),
                                Issue = GetIssueForJournal(Convert.ToInt64(reader["oidIssue"]))
                            };
                            return (T)(object)journal;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var journal = entity as Journal;
            if (journal == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO Journal (oid, __version, note, dateCreation, prive, interne, oidIssue) VALUES (@oid, @__version, @note, @dateCreation, @prive, @interne, @oidIssue)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", journal.Oid);
                    command.Parameters.AddWithValue("@__version", journal.__version);
                    command.Parameters.AddWithValue("@note", journal.Note ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", journal.DateCreation);
                    command.Parameters.AddWithValue("@prive", journal.Prive);
                    command.Parameters.AddWithValue("@interne", journal.Interne);
                    command.Parameters.AddWithValue("@oidIssue", journal.Issue?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var journal = entity as Journal;
            if (journal == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE Journal SET __version=@__version, note=@note, dateCreation=@dateCreation, prive=@prive, interne=@interne, oidIssue=@oidIssue WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", journal.Oid);
                    command.Parameters.AddWithValue("@__version", journal.__version);
                    command.Parameters.AddWithValue("@note", journal.Note ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", journal.DateCreation);
                    command.Parameters.AddWithValue("@prive", journal.Prive);
                    command.Parameters.AddWithValue("@interne", journal.Interne);
                    command.Parameters.AddWithValue("@oidIssue", journal.Issue?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }
            var journal = entity as Journal;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Journal WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", journal.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Journal))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Journal).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM Journal WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private Issue GetIssueForJournal(long oidIssue)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM Issues WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidIssue);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Issue()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Sujet = reader["sujet"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                DatePrevisionnelleDebut = Convert.ToDateTime(reader["datePrevisionnelleDebut"]),
                                DatePrevisionnelleFin = Convert.ToDateTime(reader["datePrevisionnelleFin"]),
                                DateEffectiveDebut = Convert.ToDateTime(reader["dateEffectiveDebut"]),
                                DateEffectiveFin = Convert.ToDateTime(reader["dateEffectiveFin"]),
                                KindOfActivite = reader["kindOfActivite"].ToString() ?? string.Empty,
                                KindOfIssueParente = reader["kindOfIssueParente"].ToString() ?? string.Empty,
                                Avancement = Convert.ToByte(reader["avancement"]),
                                TempsEstimeMinutes = Convert.ToInt32(reader["tempsEstimeMinutes"]),
                                TempsEffectifMinutes = Convert.ToInt32(reader["tempsEffectifMinutes"])
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
            return null;
        }
    }
}
