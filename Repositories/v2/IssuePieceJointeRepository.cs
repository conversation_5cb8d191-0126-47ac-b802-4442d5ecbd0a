using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssuePieceJointeRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }
            HashSet<IssuePieceJointe> issuePieceJointes = new HashSet<IssuePieceJointe>();

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssuePieceJointe", this._db.MySQLConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IssuePieceJointe issuePieceJointe = new IssuePieceJointe()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Fichier = reader["fichier"].ToString() ?? string.Empty,
                                TypeMime = reader["typeMime"].ToString() ?? string.Empty,
                                Taille = Convert.ToInt64(reader["taille"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Issue = GetIssueForPieceJointe(Convert.ToInt64(reader["oidIssue"])),
                                Journal = GetJournalForPieceJointe(Convert.ToInt64(reader["oidJournal"]))
                            };

                            issuePieceJointes.Add(issuePieceJointe);
                        }
                    }
                }

                return issuePieceJointes.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }

            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM IssuePieceJointe WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            IssuePieceJointe issuePieceJointe = new IssuePieceJointe()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Fichier = reader["fichier"].ToString() ?? string.Empty,
                                TypeMime = reader["typeMime"].ToString() ?? string.Empty,
                                Taille = Convert.ToInt64(reader["taille"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Issue = GetIssueForPieceJointe(Convert.ToInt64(reader["oidIssue"])),
                                Journal = GetJournalForPieceJointe(Convert.ToInt64(reader["oidJournal"]))
                            };
                            return (T)(object)issuePieceJointe;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issuePieceJointe = entity as IssuePieceJointe;
            if (issuePieceJointe == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"INSERT INTO IssuePieceJointe (oid, __version, libelle, description, fichier, typeMime, taille, dateCreation, oidIssue, oidJournal) VALUES (@oid, @__version, @libelle, @description, @fichier, @typeMime, @taille, @dateCreation, @oidIssue, @oidJournal)";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePieceJointe.Oid);
                    command.Parameters.AddWithValue("@__version", issuePieceJointe.__version);
                    command.Parameters.AddWithValue("@libelle", issuePieceJointe.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issuePieceJointe.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fichier", issuePieceJointe.Fichier ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@typeMime", issuePieceJointe.TypeMime ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@taille", issuePieceJointe.Taille);
                    command.Parameters.AddWithValue("@dateCreation", issuePieceJointe.DateCreation);
                    command.Parameters.AddWithValue("@oidIssue", issuePieceJointe.Issue?.Oid ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidJournal", issuePieceJointe.Journal?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issuePieceJointe = entity as IssuePieceJointe;
            if (issuePieceJointe == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            try
            {
                this._db.MySQLConn.Open();
                string sql = @"UPDATE IssuePieceJointe SET __version=@__version, libelle=@libelle, description=@description, fichier=@fichier, typeMime=@typeMime, taille=@taille, dateCreation=@dateCreation, oidIssue=@oidIssue, oidJournal=@oidJournal WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePieceJointe.Oid);
                    command.Parameters.AddWithValue("@__version", issuePieceJointe.__version);
                    command.Parameters.AddWithValue("@libelle", issuePieceJointe.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issuePieceJointe.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fichier", issuePieceJointe.Fichier ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@typeMime", issuePieceJointe.TypeMime ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@taille", issuePieceJointe.Taille);
                    command.Parameters.AddWithValue("@dateCreation", issuePieceJointe.DateCreation);
                    command.Parameters.AddWithValue("@oidIssue", issuePieceJointe.Issue?.Oid ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidJournal", issuePieceJointe.Journal?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }
            var issuePieceJointe = entity as IssuePieceJointe;
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssuePieceJointe WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePieceJointe.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(IssuePieceJointe))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePieceJointe).Name);
            }
            try
            {
                this._db.MySQLConn.Open();
                using (var command = new MySqlCommand("DELETE FROM IssuePieceJointe WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                this._db.MySQLConn.Close();
            }
        }

        private Issue GetIssueForPieceJointe(long oidIssue)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM Issues WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidIssue);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Issue()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Sujet = reader["sujet"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                DatePrevisionnelleDebut = Convert.ToDateTime(reader["datePrevisionnelleDebut"]),
                                DatePrevisionnelleFin = Convert.ToDateTime(reader["datePrevisionnelleFin"]),
                                DateEffectiveDebut = Convert.ToDateTime(reader["dateEffectiveDebut"]),
                                DateEffectiveFin = Convert.ToDateTime(reader["dateEffectiveFin"]),
                                KindOfActivite = reader["kindOfActivite"].ToString() ?? string.Empty,
                                KindOfIssueParente = reader["kindOfIssueParente"].ToString() ?? string.Empty,
                                Avancement = Convert.ToByte(reader["avancement"]),
                                TempsEstimeMinutes = Convert.ToInt32(reader["tempsEstimeMinutes"]),
                                TempsEffectifMinutes = Convert.ToInt32(reader["tempsEffectifMinutes"])
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
            return null;
        }

        private Journal GetJournalForPieceJointe(long oidJournal)
        {
            bool shouldCloseConnection = this._db.MySQLConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySQLConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM Journal WHERE oid = @oid", this._db.MySQLConn))
                {
                    command.Parameters.AddWithValue("@oid", oidJournal);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Journal()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Prive = Convert.ToBoolean(reader["prive"]),
                                Interne = Convert.ToBoolean(reader["interne"])
                                // Note: Not loading Issue here to avoid circular reference
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySQLConn.Close();
            }
            return null;
        }
    }
}
