namespace HeliosETL.Models.v2;

public class IssuePieceJointe
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Libelle { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Fichier { get; set; } = string.Empty;
    public string TypeMime { get; set; } = string.Empty;
    public long Taille { get; set; }
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public Issue Issue { get; set; }
    public Journal Journal { get; set; }
}